<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile页面图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .icon-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .icon-preview {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .icon-preview svg {
            width: 24px;
            height: 24px;
        }
        .icon-name {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #2E5C8A;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        .instructions code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>心安AI - Profile页面图标生成器</h1>
        
        <div class="icon-grid">
            <!-- 编辑图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                </div>
                <div class="icon-name">edit.png</div>
                <button class="download-btn" onclick="downloadIcon('edit')">下载</button>
            </div>

            <!-- 关闭图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </div>
                <div class="icon-name">close.png</div>
                <button class="download-btn" onclick="downloadIcon('close')">下载</button>
            </div>

            <!-- 图表图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                </div>
                <div class="icon-name">chart.png</div>
                <button class="download-btn" onclick="downloadIcon('chart')">下载</button>
            </div>

            <!-- 通知图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                    </svg>
                </div>
                <div class="icon-name">notification.png</div>
                <button class="download-btn" onclick="downloadIcon('notification')">下载</button>
            </div>

            <!-- 主题图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div class="icon-name">theme.png</div>
                <button class="download-btn" onclick="downloadIcon('theme')">下载</button>
            </div>

            <!-- 隐私图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                    </svg>
                </div>
                <div class="icon-name">privacy.png</div>
                <button class="download-btn" onclick="downloadIcon('privacy')">下载</button>
            </div>

            <!-- 导出图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"/>
                    </svg>
                </div>
                <div class="icon-name">export.png</div>
                <button class="download-btn" onclick="downloadIcon('export')">下载</button>
            </div>

            <!-- 删除图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#E74C3C">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                    </svg>
                </div>
                <div class="icon-name">delete.png</div>
                <button class="download-btn" onclick="downloadIcon('delete')">下载</button>
            </div>

            <!-- 帮助图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                    </svg>
                </div>
                <div class="icon-name">help.png</div>
                <button class="download-btn" onclick="downloadIcon('help')">下载</button>
            </div>

            <!-- 反馈图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z"/>
                    </svg>
                </div>
                <div class="icon-name">feedback.png</div>
                <button class="download-btn" onclick="downloadIcon('feedback')">下载</button>
            </div>

            <!-- 关于图标 -->
            <div class="icon-item">
                <div class="icon-preview">
                    <svg viewBox="0 0 24 24" fill="#666">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                </div>
                <div class="icon-name">about.png</div>
                <button class="download-btn" onclick="downloadIcon('about')">下载</button>
            </div>
        </div>

        <div class="instructions">
            <h3>使用说明</h3>
            <ol>
                <li>点击"下载"按钮，会自动下载对应的PNG图标文件</li>
                <li>将下载的图标文件放置到小程序项目的 <code>images/icons/</code> 目录下</li>
                <li>图标尺寸为 48px × 48px，适配小程序使用</li>
                <li>所有图标基于 Ant Design 设计规范</li>
            </ol>
            <p><strong>注意：</strong>如果自动下载不工作，可以右键点击图标选择"另存为"手动保存。</p>
        </div>
    </div>

    <script>
        const iconPaths = {
            edit: 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z',
            close: 'M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z',
            chart: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z',
            notification: 'M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z',
            theme: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
            privacy: 'M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z',
            export: 'M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z',
            delete: 'M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z',
            help: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z',
            feedback: 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z',
            about: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'
        };

        function downloadIcon(iconName) {
            const pathData = iconPaths[iconName];
            const color = iconName === 'delete' ? '#E74C3C' : '#666666';
            
            generateAndDownload(iconName, pathData, color);
        }

        function generateAndDownload(iconName, pathData, color) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = 48;
            
            canvas.width = size;
            canvas.height = size;
            
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="${pathData}" fill="${color}"/>
                </svg>
            `;
            
            const img = new Image();
            const svgBlob = new Blob([svg], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `${iconName}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
